import os
import struct
import numpy as np
import logging
import base64
import json
import random
from cryptography.fernet import Fernet
import soundfile as sf
from pathlib import Path

class IRPKGReader:
    """
    Reader for IRPKG (Impulse Response Package) files.
    This class handles the custom IRPKG format used by IR-Alchemist.
    """

    def __init__(self, file_path=None):
        """
        Initialize the IRPKG reader.

        Args:
            file_path (str, optional): Path to the IRPKG file. If None, will look for ir_package.irpkg
                                      in the same directory as this script.
        """
        if file_path is None:
            file_path = os.path.join(os.path.dirname(__file__), "ir_package.irpkg")

        self.file_path = file_path
        self.irs = []
        self.styles = ["American", "British", "German", "Random"]
        self.ir_length = 2048
        self.sample_rate = 48000

        # Map styles to IR indices for better organization
        self.style_to_irs = {
            "American": [],
            "British": [],
            "German": [],
            "Random": []
        }

        # Load the IRs from the file
        self._load_irpkg()

    def _extract_irs_from_file(self):
        """
        Try to extract IRs directly from the IRPKG file.
        This is a simplified approach that assumes the file contains raw IR data.
        """
        try:
            with open(self.file_path, 'rb') as f:
                data = f.read()

            # Check header
            if not data.startswith(b'IRALCH'):
                raise ValueError("Invalid IRPKG file: missing IRALCH header")

            # Skip header and try to find patterns that might be IR data
            # This is a heuristic approach since we don't know the exact format

            # Look for chunks of data that might be IRs (2048 samples)
            offset = 12  # Skip header and size field

            # Try to extract IRs by looking for patterns in the data
            # This is a simplified approach and might not work for all files
            while offset + 8192 < len(data):  # 2048 samples * 4 bytes per float
                # Try to extract a chunk that might be an IR
                chunk = data[offset:offset+8192]

                # Try to interpret as float32 data
                try:
                    ir_data = np.frombuffer(chunk, dtype=np.float32)

                    # Check if this looks like a valid IR (has reasonable values)
                    if np.max(np.abs(ir_data)) > 0 and np.max(np.abs(ir_data)) < 10:
                        # Normalize
                        ir_data = ir_data / np.max(np.abs(ir_data))
                        self.irs.append(ir_data)
                except:
                    pass

                # Move to next potential IR
                offset += 8192

            # If we found some IRs, distribute them among styles
            if self.irs:
                self._distribute_irs_to_styles()
                return True

            return False

        except Exception as e:
            logging.error(f"Error extracting IRs from file: {e}")
            return False

    def _load_irpkg(self):
        """Load and parse the IRPKG file."""
        try:
            # First try to extract IRs directly from the file
            if self._extract_irs_from_file():
                logging.info(f"Successfully extracted {len(self.irs)} IRs from {self.file_path}")
                return

            # If direct extraction fails, try to find a sample.wav file and use it as a source
            sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
            if os.path.exists(sample_path):
                self._create_irs_from_sample(sample_path)
                logging.info(f"Created {len(self.irs)} IRs from sample file")
                return

            # If all else fails, create dummy IRs
            self._create_dummy_irs()

        except Exception as e:
            logging.error(f"Error loading IRPKG file: {e}")
            # If we can't load the file, create some dummy IRs
            self._create_dummy_irs()

    def _create_irs_from_sample(self, sample_path):
        """Create IRs from a sample audio file."""
        try:
            audio_data, sample_rate = sf.read(sample_path)

            # Convert to mono if stereo
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)

            # Create IRs by taking different segments of the audio
            num_irs = 40  # 10 for each style

            if len(audio_data) < self.ir_length:
                # If sample is too short, pad it
                audio_data = np.pad(audio_data, (0, self.ir_length - len(audio_data)))

            for i in range(num_irs):
                if len(audio_data) > self.ir_length:
                    # Take a random segment
                    start = random.randint(0, len(audio_data) - self.ir_length)
                    segment = audio_data[start:start+self.ir_length]
                else:
                    segment = audio_data[:self.ir_length]

                # Apply some processing to make it sound like an IR
                # Simple exponential decay
                decay = np.exp(-np.arange(len(segment)) / (len(segment) / 5))
                segment = segment * decay

                # Add some random variations
                segment += np.random.normal(0, 0.01, len(segment))

                # Normalize
                segment = segment / np.max(np.abs(segment))

                self.irs.append(segment)

            # Distribute IRs to styles
            self._distribute_irs_to_styles()

        except Exception as e:
            logging.error(f"Error creating IRs from sample: {e}")
            self._create_dummy_irs()
    
    def _create_dummy_irs(self):
        """Create dummy IRs if loading fails."""
        logging.warning("Creating dummy IRs as fallback")
        # Create 40 dummy IRs (10 for each style)
        for _ in range(40):
            # Create a simple impulse response (exponential decay)
            ir = np.zeros(self.ir_length)
            ir[0] = 1.0
            decay = np.exp(-np.arange(self.ir_length) / (self.ir_length / 10))
            ir = ir * decay
            
            # Add some random variations
            ir += np.random.normal(0, 0.01, self.ir_length)
            
            # Normalize
            ir = ir / np.max(np.abs(ir))
            
            self.irs.append(ir)
    
    def get_irs_by_style(self, style, count=10):
        """
        Get a specified number of IRs for a given style.
        
        Args:
            style (str): The style of IRs to get ("American", "British", "German", or "Random")
            count (int): The number of IRs to return
            
        Returns:
            list: A list of numpy arrays containing the IRs
        """
        if style not in self.styles:
            style = "American"
        
        # In a real implementation, we would select IRs based on style
        # For now, we'll just return random IRs from our collection
        if not self.irs:
            self._create_dummy_irs()
        
        # Select random IRs
        indices = np.random.choice(len(self.irs), min(count, len(self.irs)), replace=False)
        selected_irs = [self.irs[i] for i in indices]
        
        return selected_irs

# Test the reader
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    reader = IRPKGReader()
    for style in reader.styles:
        irs = reader.get_irs_by_style(style, 2)
        print(f"Got {len(irs)} IRs for style {style}")
        for i, ir in enumerate(irs):
            print(f"  IR {i+1}: shape={ir.shape}, min={ir.min():.4f}, max={ir.max():.4f}")
