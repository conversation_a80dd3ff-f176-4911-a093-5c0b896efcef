import os
import numpy as np
import logging
import soundfile as sf
from pathlib import Path
import random

class IRPKGReader:
    """
    Reader for IR (Impulse Response) files from a directory.
    This class loads IR files from a specified directory and organizes them by style.
    """
    
    def __init__(self, ir_dir="E:\\IRs\\IR-Alchemist IRs"):
        """
        Initialize the IR reader.
        
        Args:
            ir_dir (str): Path to the directory containing IR files.
        """
        self.ir_dir = ir_dir
        self.irs = []
        self.styles = ["American", "British", "German", "Random"]
        self.ir_length = 2048
        self.sample_rate = 48000
        
        # Map styles to IR indices for better organization
        self.style_to_irs = {
            "American": [],
            "British": [],
            "German": [],
            "Random": []
        }
        
        # Load the IRs from the directory
        self._load_irs()
    
    def _load_irs(self):
        """Load IR files from the specified directory."""
        try:
            # Check if the directory exists
            if not os.path.exists(self.ir_dir):
                logging.warning(f"IR directory {self.ir_dir} does not exist")
                self._create_dummy_irs()
                return
            
            # Find all WAV files in the directory
            ir_files = []
            for ext in ['*.wav', '*.WAV']:
                ir_files.extend(list(Path(self.ir_dir).rglob(ext)))
            
            if not ir_files:
                logging.warning(f"No IR files found in {self.ir_dir}")
                self._create_dummy_irs()
                return
            
            logging.info(f"Found {len(ir_files)} IR files in {self.ir_dir}")
            
            # Load each IR file
            for ir_path in ir_files:
                try:
                    # Load audio data
                    audio, sr = sf.read(str(ir_path))
                    
                    # Convert to mono if stereo
                    if len(audio.shape) > 1:
                        audio = np.mean(audio, axis=1)
                    
                    # Resample if necessary
                    if sr != self.sample_rate:
                        # Simple resampling by interpolation
                        orig_len = len(audio)
                        target_len = int(orig_len * (self.sample_rate / sr))
                        indices = np.linspace(0, orig_len - 1, target_len)
                        audio = np.interp(indices, np.arange(orig_len), audio)
                    
                    # Ensure the IR is the correct length
                    if len(audio) > self.ir_length:
                        audio = audio[:self.ir_length]
                    elif len(audio) < self.ir_length:
                        audio = np.pad(audio, (0, self.ir_length - len(audio)))
                    
                    # Normalize
                    max_val = np.max(np.abs(audio))
                    if max_val > 0:
                        audio = audio / max_val
                    
                    self.irs.append(audio)
                    
                except Exception as e:
                    logging.error(f"Error loading IR file {ir_path}: {e}")
            
            # If we loaded some IRs, distribute them among styles
            if self.irs:
                self._distribute_irs_to_styles()
            else:
                self._create_dummy_irs()
                
            logging.info(f"Successfully loaded {len(self.irs)} IRs")
            
        except Exception as e:
            logging.error(f"Error loading IR files: {e}")
            self._create_dummy_irs()
    
    def _distribute_irs_to_styles(self):
        """Distribute loaded IRs among different styles."""
        # Shuffle the IRs for random distribution
        shuffled_indices = list(range(len(self.irs)))
        random.shuffle(shuffled_indices)
        
        # Distribute IRs evenly among styles
        irs_per_style = len(self.irs) // len(self.styles)
        remainder = len(self.irs) % len(self.styles)
        
        start_idx = 0
        for i, style in enumerate(self.styles):
            # Give extra IRs to the first few styles if there's a remainder
            count = irs_per_style + (1 if i < remainder else 0)
            end_idx = start_idx + count
            
            self.style_to_irs[style] = shuffled_indices[start_idx:end_idx]
            start_idx = end_idx
    
    def _create_dummy_irs(self):
        """Create dummy IRs if loading fails."""
        logging.warning("Creating dummy IRs as fallback")
        # Create 40 dummy IRs (10 for each style)
        for _ in range(40):
            # Create a simple impulse response (exponential decay)
            ir = np.zeros(self.ir_length)
            ir[0] = 1.0
            decay = np.exp(-np.arange(self.ir_length) / (self.ir_length / 10))
            ir = ir * decay
            
            # Add some random variations
            ir += np.random.normal(0, 0.01, self.ir_length)
            
            # Normalize
            ir = ir / np.max(np.abs(ir))
            
            self.irs.append(ir)
        
        # Distribute dummy IRs to styles
        self._distribute_irs_to_styles()
    
    def get_irs_by_style(self, style, count=10):
        """
        Get a specified number of IRs for a given style.
        
        Args:
            style (str): The style of IRs to get ("American", "British", "German", or "Random")
            count (int): The number of IRs to return
            
        Returns:
            list: A list of numpy arrays containing the IRs
        """
        if style not in self.styles:
            style = "American"
        
        # Get IRs for the specified style
        style_indices = self.style_to_irs[style]
        
        if not style_indices:
            # If no IRs for this style, use all IRs
            style_indices = list(range(len(self.irs)))
        
        # Select random IRs from the style
        selected_count = min(count, len(style_indices))
        selected_indices = random.sample(style_indices, selected_count)
        
        # If we need more IRs than available, repeat some
        while len(selected_indices) < count and style_indices:
            additional_needed = count - len(selected_indices)
            additional_indices = random.choices(style_indices, k=additional_needed)
            selected_indices.extend(additional_indices)
        
        selected_irs = [self.irs[i] for i in selected_indices[:count]]
        
        return selected_irs

# Test the reader
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    reader = IRPKGReader()
    for style in reader.styles:
        irs = reader.get_irs_by_style(style, 2)
        print(f"Got {len(irs)} IRs for style {style}")
        for i, ir in enumerate(irs):
            print(f"  IR {i+1}: shape={ir.shape}, min={ir.min():.4f}, max={ir.max():.4f}")
