#!/usr/bin/env python3
"""
Native PyQt5 Visualization System for IR-Alchemist
Replaces matplotlib with custom PyQt5-based plotting capabilities.
"""

import math
import numpy as np
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PyQt5.QtCore import Qt, QRect, QPoint, QSize
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QLinearGradient, QPolygonF, QFontMetrics

class FrequencyResponsePlot(QWidget):
    """
    Custom frequency response plot using PyQt5 native graphics.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 200)
        self.setStyleSheet("background-color: #121212; border: 1px solid #333333;")
        
        # Data
        self.frequencies = None
        self.magnitudes_db = None
        
        # Plot settings
        self.margin_left = 60
        self.margin_right = 20
        self.margin_top = 20
        self.margin_bottom = 40
        
        # Colors
        self.bg_color = QColor(18, 18, 18)
        self.grid_color = QColor(60, 60, 60)
        self.curve_color = QColor(0, 212, 170)  # Teal
        self.text_color = QColor(200, 200, 200)
        self.axis_color = QColor(120, 120, 120)
    
    def set_data(self, frequencies, magnitudes_db):
        """Set the frequency response data."""
        self.frequencies = frequencies
        self.magnitudes_db = magnitudes_db
        self.update()
    
    def paintEvent(self, event):
        """Paint the frequency response plot."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Fill background
        painter.fillRect(self.rect(), self.bg_color)
        
        if self.frequencies is None or self.magnitudes_db is None:
            self._draw_placeholder(painter)
            return
        
        # Calculate plot area
        plot_rect = QRect(
            self.margin_left,
            self.margin_top,
            self.width() - self.margin_left - self.margin_right,
            self.height() - self.margin_top - self.margin_bottom
        )
        
        # Draw grid and axes
        self._draw_grid(painter, plot_rect)
        self._draw_axes(painter, plot_rect)
        
        # Draw frequency response curve
        self._draw_curve(painter, plot_rect)
        
        # Draw labels
        self._draw_labels(painter, plot_rect)
    
    def _draw_placeholder(self, painter):
        """Draw placeholder text when no data is available."""
        painter.setPen(QPen(self.text_color))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(self.rect(), Qt.AlignCenter, "Frequency Response\n(No data)")
    
    def _draw_grid(self, painter, plot_rect):
        """Draw grid lines."""
        painter.setPen(QPen(self.grid_color, 1, Qt.DotLine))
        
        # Vertical grid lines (frequency)
        freq_decades = [20, 100, 1000, 10000]
        for freq in freq_decades:
            if freq >= self.frequencies[0] and freq <= self.frequencies[-1]:
                x = int(self._freq_to_x(freq, plot_rect))
                painter.drawLine(x, plot_rect.top(), x, plot_rect.bottom())
        
        # Horizontal grid lines (magnitude)
        mag_range = np.max(self.magnitudes_db) - np.min(self.magnitudes_db)
        if mag_range > 0:
            step = max(10, int(mag_range / 6) // 10 * 10)
            min_mag = int(np.min(self.magnitudes_db) // step) * step
            max_mag = int(np.max(self.magnitudes_db) // step + 1) * step
            
            for mag in range(min_mag, max_mag + 1, step):
                y = int(self._mag_to_y(mag, plot_rect))
                if plot_rect.top() <= y <= plot_rect.bottom():
                    painter.drawLine(plot_rect.left(), y, plot_rect.right(), y)
    
    def _draw_axes(self, painter, plot_rect):
        """Draw axes."""
        painter.setPen(QPen(self.axis_color, 2))
        
        # X-axis (bottom)
        painter.drawLine(plot_rect.left(), plot_rect.bottom(), plot_rect.right(), plot_rect.bottom())
        
        # Y-axis (left)
        painter.drawLine(plot_rect.left(), plot_rect.top(), plot_rect.left(), plot_rect.bottom())
    
    def _draw_curve(self, painter, plot_rect):
        """Draw the frequency response curve."""
        if len(self.frequencies) < 2:
            return
        
        # Create curve path
        points = []
        for i, (freq, mag) in enumerate(zip(self.frequencies, self.magnitudes_db)):
            x = self._freq_to_x(freq, plot_rect)
            y = self._mag_to_y(mag, plot_rect)
            points.append(QPoint(int(x), int(y)))
        
        # Draw curve with gradient
        painter.setPen(QPen(self.curve_color, 2))
        
        for i in range(len(points) - 1):
            painter.drawLine(points[i], points[i + 1])
    
    def _draw_labels(self, painter, plot_rect):
        """Draw axis labels."""
        painter.setPen(QPen(self.text_color))
        painter.setFont(QFont("Arial", 9))
        
        # Frequency labels (X-axis)
        freq_labels = [20, 100, 1000, 10000]
        for freq in freq_labels:
            if freq >= self.frequencies[0] and freq <= self.frequencies[-1]:
                x = int(self._freq_to_x(freq, plot_rect))
                label = f"{freq//1000}k" if freq >= 1000 else str(freq)
                painter.drawText(x - 15, plot_rect.bottom() + 15, label)
        
        # Magnitude labels (Y-axis)
        mag_range = np.max(self.magnitudes_db) - np.min(self.magnitudes_db)
        if mag_range > 0:
            step = max(10, int(mag_range / 4) // 10 * 10)
            min_mag = int(np.min(self.magnitudes_db) // step) * step
            max_mag = int(np.max(self.magnitudes_db) // step + 1) * step
            
            for mag in range(min_mag, max_mag + 1, step):
                y = int(self._mag_to_y(mag, plot_rect))
                if plot_rect.top() <= y <= plot_rect.bottom():
                    painter.drawText(5, y + 5, f"{mag}")
        
        # Axis titles
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        painter.drawText(plot_rect.center().x() - 30, self.height() - 5, "Frequency (Hz)")
        
        # Rotate for Y-axis label
        painter.save()
        painter.translate(15, plot_rect.center().y())
        painter.rotate(-90)
        painter.drawText(-30, 0, "Magnitude (dB)")
        painter.restore()
    
    def _freq_to_x(self, freq, plot_rect):
        """Convert frequency to X coordinate (logarithmic scale)."""
        if freq <= 0:
            return plot_rect.left()
        
        log_freq = math.log10(freq)
        log_min = math.log10(max(self.frequencies[0], 1))
        log_max = math.log10(self.frequencies[-1])
        
        if log_max <= log_min:
            return plot_rect.left()
        
        ratio = (log_freq - log_min) / (log_max - log_min)
        return plot_rect.left() + ratio * plot_rect.width()
    
    def _mag_to_y(self, mag, plot_rect):
        """Convert magnitude to Y coordinate."""
        mag_min = np.min(self.magnitudes_db)
        mag_max = np.max(self.magnitudes_db)
        
        if mag_max <= mag_min:
            return plot_rect.center().y()
        
        ratio = (mag - mag_min) / (mag_max - mag_min)
        return plot_rect.bottom() - ratio * plot_rect.height()

class WaveformPlot(QWidget):
    """
    Custom time-domain waveform plot using PyQt5 native graphics.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 200)
        self.setStyleSheet("background-color: #121212; border: 1px solid #333333;")
        
        # Data
        self.waveform = None
        self.sample_rate = 48000
        
        # Plot settings
        self.margin_left = 50
        self.margin_right = 20
        self.margin_top = 20
        self.margin_bottom = 40
        
        # Colors
        self.bg_color = QColor(18, 18, 18)
        self.grid_color = QColor(60, 60, 60)
        self.wave_color = QColor(0, 116, 204)  # Blue
        self.text_color = QColor(200, 200, 200)
        self.axis_color = QColor(120, 120, 120)
    
    def set_data(self, waveform, sample_rate=48000):
        """Set the waveform data."""
        self.waveform = waveform
        self.sample_rate = sample_rate
        self.update()
    
    def paintEvent(self, event):
        """Paint the waveform plot."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Fill background
        painter.fillRect(self.rect(), self.bg_color)
        
        if self.waveform is None:
            self._draw_placeholder(painter)
            return
        
        # Calculate plot area
        plot_rect = QRect(
            self.margin_left,
            self.margin_top,
            self.width() - self.margin_left - self.margin_right,
            self.height() - self.margin_top - self.margin_bottom
        )
        
        # Draw grid and axes
        self._draw_grid(painter, plot_rect)
        self._draw_axes(painter, plot_rect)
        
        # Draw waveform
        self._draw_waveform(painter, plot_rect)
        
        # Draw labels
        self._draw_labels(painter, plot_rect)
    
    def _draw_placeholder(self, painter):
        """Draw placeholder text when no data is available."""
        painter.setPen(QPen(self.text_color))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(self.rect(), Qt.AlignCenter, "Time Domain\n(No data)")
    
    def _draw_grid(self, painter, plot_rect):
        """Draw grid lines."""
        painter.setPen(QPen(self.grid_color, 1, Qt.DotLine))
        
        # Vertical grid lines (time)
        duration = len(self.waveform) / self.sample_rate
        time_step = duration / 5
        
        for i in range(6):
            time = i * time_step
            x = int(self._time_to_x(time, plot_rect, duration))
            painter.drawLine(x, plot_rect.top(), x, plot_rect.bottom())
        
        # Horizontal grid lines (amplitude)
        amp_max = np.max(np.abs(self.waveform))
        if amp_max > 0:
            for amp in [-amp_max, -amp_max/2, 0, amp_max/2, amp_max]:
                y = int(self._amp_to_y(amp, plot_rect))
                painter.drawLine(plot_rect.left(), y, plot_rect.right(), y)
    
    def _draw_axes(self, painter, plot_rect):
        """Draw axes."""
        painter.setPen(QPen(self.axis_color, 2))
        
        # X-axis (bottom)
        painter.drawLine(plot_rect.left(), plot_rect.bottom(), plot_rect.right(), plot_rect.bottom())
        
        # Y-axis (left)
        painter.drawLine(plot_rect.left(), plot_rect.top(), plot_rect.left(), plot_rect.bottom())
        
        # Zero line
        y_zero = int(self._amp_to_y(0, plot_rect))
        painter.setPen(QPen(self.axis_color, 1))
        painter.drawLine(plot_rect.left(), y_zero, plot_rect.right(), y_zero)
    
    def _draw_waveform(self, painter, plot_rect):
        """Draw the waveform."""
        if len(self.waveform) < 2:
            return
        
        duration = len(self.waveform) / self.sample_rate
        
        # Downsample if necessary for performance
        max_points = plot_rect.width()
        step = max(1, len(self.waveform) // max_points)
        
        points = []
        for i in range(0, len(self.waveform), step):
            time = i / self.sample_rate
            amp = self.waveform[i]
            x = self._time_to_x(time, plot_rect, duration)
            y = self._amp_to_y(amp, plot_rect)
            points.append(QPoint(int(x), int(y)))
        
        # Draw waveform
        painter.setPen(QPen(self.wave_color, 1))
        for i in range(len(points) - 1):
            painter.drawLine(points[i], points[i + 1])
    
    def _draw_labels(self, painter, plot_rect):
        """Draw axis labels."""
        painter.setPen(QPen(self.text_color))
        painter.setFont(QFont("Arial", 9))
        
        # Time labels (X-axis)
        duration = len(self.waveform) / self.sample_rate
        time_step = duration / 5
        
        for i in range(6):
            time = i * time_step
            x = int(self._time_to_x(time, plot_rect, duration))
            label = f"{time:.2f}s"
            painter.drawText(x - 15, plot_rect.bottom() + 15, label)
        
        # Amplitude labels (Y-axis)
        amp_max = np.max(np.abs(self.waveform))
        if amp_max > 0:
            for amp in [-amp_max, 0, amp_max]:
                y = int(self._amp_to_y(amp, plot_rect))
                painter.drawText(5, y + 5, f"{amp:.2f}")
        
        # Axis titles
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        painter.drawText(plot_rect.center().x() - 20, self.height() - 5, "Time (s)")
        
        # Y-axis label
        painter.save()
        painter.translate(15, plot_rect.center().y())
        painter.rotate(-90)
        painter.drawText(-25, 0, "Amplitude")
        painter.restore()
    
    def _time_to_x(self, time, plot_rect, duration):
        """Convert time to X coordinate."""
        if duration <= 0:
            return plot_rect.left()
        
        ratio = time / duration
        return plot_rect.left() + ratio * plot_rect.width()
    
    def _amp_to_y(self, amp, plot_rect):
        """Convert amplitude to Y coordinate."""
        amp_max = np.max(np.abs(self.waveform))
        if amp_max <= 0:
            return plot_rect.center().y()
        
        # Normalize amplitude to [-1, 1] range
        norm_amp = amp / amp_max
        
        # Convert to Y coordinate (flip because Y increases downward)
        return plot_rect.center().y() - norm_amp * plot_rect.height() / 2

class SpectrogramPlot(QWidget):
    """
    Custom spectrogram-like visualization using PyQt5 native graphics.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 200)
        self.setStyleSheet("background-color: #121212; border: 1px solid #333333;")

        # Data
        self.waveform = None
        self.sample_rate = 48000

        # Plot settings
        self.margin_left = 50
        self.margin_right = 20
        self.margin_top = 20
        self.margin_bottom = 40

        # Colors
        self.bg_color = QColor(18, 18, 18)
        self.text_color = QColor(200, 200, 200)
        self.axis_color = QColor(120, 120, 120)

    def set_data(self, waveform, sample_rate=48000):
        """Set the waveform data for spectrogram analysis."""
        self.waveform = waveform
        self.sample_rate = sample_rate
        self.update()

    def paintEvent(self, event):
        """Paint the spectrogram plot."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Fill background
        painter.fillRect(self.rect(), self.bg_color)

        if self.waveform is None:
            self._draw_placeholder(painter)
            return

        # Calculate plot area
        plot_rect = QRect(
            self.margin_left,
            self.margin_top,
            self.width() - self.margin_left - self.margin_right,
            self.height() - self.margin_top - self.margin_bottom
        )

        # Draw spectrogram
        self._draw_spectrogram(painter, plot_rect)

        # Draw axes and labels
        self._draw_axes(painter, plot_rect)
        self._draw_labels(painter, plot_rect)

    def _draw_placeholder(self, painter):
        """Draw placeholder text when no data is available."""
        painter.setPen(QPen(self.text_color))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(self.rect(), Qt.AlignCenter, "Spectral Analysis\n(No data)")

    def _draw_spectrogram(self, painter, plot_rect):
        """Draw a simplified spectrogram using frequency bands."""
        if len(self.waveform) < 64:
            return

        # Parameters for analysis
        window_size = 256
        hop_size = window_size // 4
        num_windows = (len(self.waveform) - window_size) // hop_size + 1

        if num_windows <= 0:
            return

        # Frequency bands (simplified)
        freq_bands = [
            (20, 200),    # Low
            (200, 800),   # Low-mid
            (800, 3200),  # Mid
            (3200, 8000), # High-mid
            (8000, 20000) # High
        ]

        # Calculate energy in each band for each time window
        band_energies = []
        for i in range(num_windows):
            start = i * hop_size
            end = start + window_size
            window = self.waveform[start:end]

            # Apply window function
            window = window * np.hanning(len(window))

            # FFT
            fft = np.fft.rfft(window)
            freqs = np.fft.rfftfreq(len(window), 1/self.sample_rate)
            magnitudes = np.abs(fft)

            # Calculate energy in each band
            energies = []
            for low_freq, high_freq in freq_bands:
                mask = (freqs >= low_freq) & (freqs <= high_freq)
                energy = np.sum(magnitudes[mask] ** 2)
                energies.append(energy)

            band_energies.append(energies)

        if not band_energies:
            return

        # Normalize energies
        band_energies = np.array(band_energies)
        max_energy = np.max(band_energies)
        if max_energy > 0:
            band_energies = band_energies / max_energy

        # Draw spectrogram as colored rectangles
        time_width = plot_rect.width() / num_windows
        freq_height = plot_rect.height() / len(freq_bands)

        for t in range(num_windows):
            for f in range(len(freq_bands)):
                energy = band_energies[t, f]

                # Color based on energy (blue to red)
                intensity = int(255 * energy)
                color = QColor(intensity, 0, 255 - intensity, 180)

                x = plot_rect.left() + t * time_width
                y = plot_rect.top() + f * freq_height

                painter.fillRect(
                    int(x), int(y),
                    int(time_width + 1), int(freq_height + 1),
                    color
                )

    def _draw_axes(self, painter, plot_rect):
        """Draw axes."""
        painter.setPen(QPen(self.axis_color, 2))

        # X-axis (bottom)
        painter.drawLine(plot_rect.left(), plot_rect.bottom(), plot_rect.right(), plot_rect.bottom())

        # Y-axis (left)
        painter.drawLine(plot_rect.left(), plot_rect.top(), plot_rect.left(), plot_rect.bottom())

    def _draw_labels(self, painter, plot_rect):
        """Draw axis labels."""
        painter.setPen(QPen(self.text_color))
        painter.setFont(QFont("Arial", 9))

        # Time labels (X-axis)
        duration = len(self.waveform) / self.sample_rate
        for i in range(5):
            time = (i / 4) * duration
            x = int(plot_rect.left() + (i / 4) * plot_rect.width())
            painter.drawText(x - 15, plot_rect.bottom() + 15, f"{time:.2f}s")

        # Frequency labels (Y-axis)
        freq_labels = ["20k", "8k", "3.2k", "800", "200", "20"]
        for i, label in enumerate(freq_labels):
            y = int(plot_rect.top() + (i / 5) * plot_rect.height())
            painter.drawText(5, y + 5, label)

        # Axis titles
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        painter.drawText(plot_rect.center().x() - 20, self.height() - 5, "Time (s)")

        # Y-axis label
        painter.save()
        painter.translate(15, plot_rect.center().y())
        painter.rotate(-90)
        painter.drawText(-30, 0, "Frequency (Hz)")
        painter.restore()

class IRAnalysisPanel(QWidget):
    """
    Panel showing detailed IR analysis information.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("background-color: #1a1a1a; border: 1px solid #333333; padding: 10px;")

        # Create layout
        layout = QVBoxLayout()
        layout.setSpacing(8)

        # Title
        self.title_label = QLabel("IR Analysis")
        self.title_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(self.title_label)

        # Analysis labels
        self.length_label = QLabel("Length: --")
        self.duration_label = QLabel("Duration: --")
        self.peak_freq_label = QLabel("Peak Frequency: --")
        self.rms_label = QLabel("RMS: --")
        self.peak_amp_label = QLabel("Peak Amplitude: --")
        self.dynamic_range_label = QLabel("Dynamic Range: --")

        labels = [
            self.length_label, self.duration_label, self.peak_freq_label,
            self.rms_label, self.peak_amp_label, self.dynamic_range_label
        ]

        for label in labels:
            label.setStyleSheet("color: #cccccc; font-size: 11px; margin: 2px 0px;")
            layout.addWidget(label)

        layout.addStretch()
        self.setLayout(layout)

    def update_analysis(self, ir, sample_rate=48000):
        """Update the analysis information."""
        if ir is None or len(ir) == 0:
            self._clear_analysis()
            return

        # Calculate analysis metrics
        length = len(ir)
        duration = length / sample_rate

        # Frequency analysis
        fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(length, 1/sample_rate)
        magnitudes = np.abs(fft)
        peak_freq_idx = np.argmax(magnitudes)
        peak_freq = freqs[peak_freq_idx]

        # Amplitude analysis
        rms = np.sqrt(np.mean(ir**2))
        peak_amp = np.max(np.abs(ir))

        # Dynamic range (in dB)
        if rms > 0 and peak_amp > 0:
            dynamic_range = 20 * np.log10(peak_amp / rms)
        else:
            dynamic_range = 0

        # Update labels
        self.length_label.setText(f"Length: {length} samples")
        self.duration_label.setText(f"Duration: {duration:.3f} seconds")
        self.peak_freq_label.setText(f"Peak Frequency: {peak_freq:.1f} Hz")
        self.rms_label.setText(f"RMS: {rms:.4f}")
        self.peak_amp_label.setText(f"Peak Amplitude: {peak_amp:.4f}")
        self.dynamic_range_label.setText(f"Dynamic Range: {dynamic_range:.1f} dB")

    def _clear_analysis(self):
        """Clear all analysis information."""
        labels = [
            self.length_label, self.duration_label, self.peak_freq_label,
            self.rms_label, self.peak_amp_label, self.dynamic_range_label
        ]

        for label in labels:
            text = label.text().split(':')[0] + ": --"
            label.setText(text)

class NativeDetailCanvas(QWidget):
    """
    Complete native PyQt5 replacement for matplotlib-based DetailCanvas.
    Provides frequency response, waveform, and spectral analysis.
    """

    def __init__(self, parent=None, dpi=100):
        super().__init__(parent)
        self.setStyleSheet("background-color: #121212;")
        self.setMinimumSize(800, 400)

        # Create main layout
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Left panel - Frequency response
        left_panel = QVBoxLayout()
        freq_title = QLabel("Frequency Response")
        freq_title.setStyleSheet("color: white; font-size: 12px; font-weight: bold; margin-bottom: 5px;")
        freq_title.setAlignment(Qt.AlignCenter)

        self.freq_plot = FrequencyResponsePlot()
        left_panel.addWidget(freq_title)
        left_panel.addWidget(self.freq_plot)

        # Right panel - Time domain and spectral analysis
        right_panel = QVBoxLayout()

        # Time domain plot
        time_title = QLabel("Time Domain")
        time_title.setStyleSheet("color: white; font-size: 12px; font-weight: bold; margin-bottom: 5px;")
        time_title.setAlignment(Qt.AlignCenter)

        self.waveform_plot = WaveformPlot()

        # Spectral analysis
        spectral_title = QLabel("Spectral Analysis")
        spectral_title.setStyleSheet("color: white; font-size: 12px; font-weight: bold; margin-bottom: 5px;")
        spectral_title.setAlignment(Qt.AlignCenter)

        self.spectrogram_plot = SpectrogramPlot()

        right_panel.addWidget(time_title)
        right_panel.addWidget(self.waveform_plot)
        right_panel.addWidget(spectral_title)
        right_panel.addWidget(self.spectrogram_plot)

        # Analysis panel
        self.analysis_panel = IRAnalysisPanel()
        self.analysis_panel.setMaximumWidth(200)

        # Add panels to main layout
        left_widget = QWidget()
        left_widget.setLayout(left_panel)

        right_widget = QWidget()
        right_widget.setLayout(right_panel)

        main_layout.addWidget(left_widget, 2)  # 2/5 of width
        main_layout.addWidget(right_widget, 2)  # 2/5 of width
        main_layout.addWidget(self.analysis_panel, 1)  # 1/5 of width

        self.setLayout(main_layout)

    def update_detail(self, ir, sample_rate=48000):
        """
        Update all visualizations with new IR data.

        Args:
            ir: Impulse response array
            sample_rate: Sample rate in Hz
        """
        if ir is None or len(ir) == 0:
            return

        # Ensure IR is numpy array
        if not isinstance(ir, np.ndarray):
            ir = np.array(ir)

        # Calculate frequency response
        n = len(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        fft = np.fft.rfft(ir)
        magnitudes_db = 20 * np.log10(np.abs(fft) + 1e-9)

        # Update frequency response plot
        self.freq_plot.set_data(freqs, magnitudes_db)

        # Update waveform plot
        self.waveform_plot.set_data(ir, sample_rate)

        # Update spectrogram plot
        self.spectrogram_plot.set_data(ir, sample_rate)

        # Update analysis panel
        self.analysis_panel.update_analysis(ir, sample_rate)

# Convenience function for easy import
def create_detail_canvas(parent=None):
    """Create a new native detail canvas."""
    return NativeDetailCanvas(parent)
