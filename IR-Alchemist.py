import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap
import logging

# Import matplotlib components safely
try:
    import matplotlib
    matplotlib.use('Qt5Agg')  # Set the backend explicitly
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Matplotlib not available: {e}")
    MATPLOTLIB_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# --- CarbonWidget: Custom widget with carbon background ---
class CarbonWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pattern = self.createCarbonPattern()

    def createCarbonPattern(self):
        # Create a 40x40 pixmap with a carbon-like pattern
        pixmap = QPixmap(40, 40)
        pixmap.fill(QColor("#2b2b2b"))  # Base dark color
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        whitePen = QPen(QColor(255, 255, 255, 15))  # ~6% opacity
        blackPen = QPen(QColor(0, 0, 0, 15))
        # Draw white lines
        painter.setPen(whitePen)
        painter.drawLine(0, 10, 30, 40)
        painter.drawLine(10, 0, 40, 30)
        # Draw black lines
        painter.setPen(blackPen)
        painter.drawLine(0, 20, 20, 40)
        painter.drawLine(20, 0, 40, 20)
        painter.end()
        return pixmap

    def paintEvent(self, event):
        painter = QPainter(self)
        brush = QBrush(self.pattern)
        painter.fillRect(self.rect(), brush)
        super().paintEvent(event)

# --- Custom Checkbox without SVG ---
class DarkCheckBox(QCheckBox):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #FFFFFF;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #555555;
                background-color: #333333;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #007ACC;
                background-color: #007ACC;
            }
        """)

# --- Detail Canvas for IR Preview ---
if MATPLOTLIB_AVAILABLE:
    class DetailCanvas(FigureCanvas):
        def __init__(self, parent=None, dpi=100):
            self.fig, (self.ax_eq, self.ax_spec) = plt.subplots(1, 2, figsize=(10, 4), dpi=dpi, facecolor='#121212')
            super(DetailCanvas, self).__init__(self.fig)
            self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            plt.tight_layout()
            self.setStyleSheet("background-color: #121212;")
else:
    class DetailCanvas(QWidget):
        def __init__(self, parent=None, dpi=100):
            super().__init__(parent)
            self.setStyleSheet("background-color: #121212; border: 1px solid gray;")
            layout = QVBoxLayout()
            self.info_label = QLabel("IR Detail View\n(Matplotlib not available)")
            self.info_label.setAlignment(Qt.AlignCenter)
            self.info_label.setStyleSheet("color: white; padding: 20px;")
            layout.addWidget(self.info_label)
            self.setLayout(layout)

    def update_detail(self, ir, sample_rate=48000):
        # Update left subplot with EQ curve (frequency response)
        self.ax_eq.clear()
        n = len(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft = np.fft.rfft(ir)
        mag = np.abs(IR_fft)
        mag_db = 20 * np.log10(mag + 1e-9)
        self.ax_eq.plot(freqs, mag_db, color='#007ACC', lw=2)
        self.ax_eq.set_xscale('log')
        self.ax_eq.set_xlim(20, sample_rate / 2)
        self.ax_eq.set_title("EQ Curve", fontsize=12, color='white')
        self.ax_eq.set_xlabel("Frequency [Hz]", color='lightgray')
        self.ax_eq.set_ylabel("Magnitude (dB)", color='lightgray')
        self.ax_eq.grid(True, which='both', ls='--', alpha=0.5, color='gray')
        self.ax_eq.tick_params(axis='x', colors='lightgray')
        self.ax_eq.tick_params(axis='y', colors='lightgray')
        self.ax_eq.set_facecolor('#121212')

        # Update right subplot with spectrogram
        self.ax_spec.clear()
        self.ax_spec.specgram(ir, Fs=sample_rate, cmap='magma')
        self.ax_spec.set_title("Spectrogram", fontsize=12, color='white')
        self.ax_spec.set_xlabel("Time [s]", color='lightgray')
        self.ax_spec.set_ylabel("Frequency [Hz]", color='lightgray')
        self.ax_spec.tick_params(axis='x', colors='lightgray')
        self.ax_spec.tick_params(axis='y', colors='lightgray')
        self.ax_spec.set_facecolor('#121212')
        self.fig.tight_layout(rect=[0, 0, 1, 1])
        self.fig.patch.set_facecolor('#121212')
        self.draw()

# --- IRExporter ---
class IRExporter:
    def __init__(self, ir_dir="E:\\IRs\\IR-Alchemist IRs"):
        # Import the IR reader
        from irpkg_reader import IRPKGReader

        # Initialize the IR dataset reader
        self.ir_reader = IRPKGReader(ir_dir)
        self.ir_length = 2048
        self.pp_params = {
            "resonators": {"min_count": 1, "max_count": 4, "gain_range": (0.8, 1.2)},
            "fixed_filters": {"hp_cutoff": 80, "lp_cutoff": 9500, "order": 3},
            "random_eq": {"num_points": 8, "gain_range": (0.5, 1.5)},
            "amp_simulation": {"drive": 1.5, "mid_band": (300, 3000), "mid_gain_range": (0.8, 1.2)},
            "suppress_resonances": {"threshold": 1.5, "window_size": 9},
            "high_shelf": {"cutoff": 4000, "shelf_gain": 1.2},
            "custom_style": {"bass_boost": 1.05, "mid_boost": 1.05, "high_boost": 1.05}
        }

    def apply_resonators(self, ir, sample_rate=48000):
        params = self.pp_params["resonators"]
        min_count = params["min_count"]
        max_count = params["max_count"]
        count = min_count if min_count == max_count else np.random.randint(min_count, max_count + 1)
        processed = np.copy(ir)
        for _ in range(count):
            fc = np.random.uniform(1000, 8000)
            Q = np.random.uniform(10, 30)
            bw = fc / Q
            low = max(fc - bw / 2, 20)
            high = min(fc + bw / 2, sample_rate / 2 - 1)
            low_norm = low / (sample_rate / 2)
            high_norm = high / (sample_rate / 2)
            try:
                b, a = butter(2, [low_norm, high_norm], btype='band')
            except ValueError as ve:
                logging.error("Error creating resonator filters: %s", str(ve))
                continue
            filtered = lfilter(b, a, ir)
            gain = np.random.uniform(*params["gain_range"])
            processed += gain * filtered
        return processed

    def apply_fixed_filters(self, ir, sample_rate=48048):
        params = self.pp_params["fixed_filters"]
        hp_norm = params["hp_cutoff"] / (sample_rate / 2)
        b_hp, a_hp = butter(params["order"], hp_norm, btype='high')
        ir_hp = lfilter(b_hp, a_hp, ir)
        lp_norm = params["lp_cutoff"] / (sample_rate / 2)
        b_lp, a_lp = butter(params["order"], lp_norm, btype='low')
        return lfilter(b_lp, a_lp, ir_hp)

    def apply_random_eq(self, ir, sample_rate=48048):
        params = self.pp_params["random_eq"]
        n = len(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft = np.fft.rfft(ir)
        freq_points = np.linspace(80, 9500, num=params["num_points"])
        random_gains = np.random.uniform(*params["gain_range"], size=params["num_points"])
        gain_curve = np.interp(freqs, freq_points, random_gains)
        kernel = np.ones(3) / 3
        smooth_curve = np.convolve(gain_curve, kernel, mode='same')
        blended_curve = 0.5 * gain_curve + 0.5 * smooth_curve
        IR_fft *= blended_curve
        return np.fft.irfft(IR_fft, n=n)

    def apply_amp_simulation(self, ir, sample_rate=48048):
        params = self.pp_params["amp_simulation"]
        drive = params["drive"]
        saturated = np.tanh(drive * ir)
        b_mid, a_mid = butter(2, [params["mid_band"][0] / (sample_rate / 2),
                                   params["mid_band"][1] / (sample_rate / 2)], btype='band')
        mid = lfilter(b_mid, a_mid, saturated)
        mid_gain = np.random.uniform(*params["mid_gain_range"])
        mid *= mid_gain
        return 0.5 * saturated + 0.5 * mid

    def suppress_unpleasant_resonances(self, ir, sample_rate=48048):
        params = self.pp_params["suppress_resonances"]
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        mag = np.abs(IR_fft)
        phase = np.angle(IR_fft)
        kernel = np.ones(params["window_size"]) / params["window_size"]
        mag_smoothed = np.convolve(mag, kernel, mode='same')
        mag_new = np.minimum(mag, params["threshold"] * mag_smoothed)
        new_fft = mag_new * np.exp(1j * phase)
        return np.fft.irfft(new_fft, n=n)

    def apply_style(self, ir, style, sample_rate=48048):
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        boost = np.ones_like(IR_fft)
        if style == "German":
            boost[(freqs >= 4000) & (freqs <= 6000)] *= 1.10
        elif style == "British":
            boost[(freqs >= 300) & (freqs <= 800)] *= 1.15
        elif style == "American":
            boost[freqs < 300] *= 1.10
        elif style == "Random":
            # For "Random", use a random modulation of the boost curve
            boost = np.random.uniform(0.8, 1.2, size=boost.shape)
        new_fft = IR_fft * boost
        return np.fft.irfft(new_fft, n=n)

    def apply_high_shelf(self, ir, sample_rate=48048):
        params = self.pp_params["high_shelf"]
        n = len(ir)
        IR_fft = np.fft.rfft(ir)
        freqs = np.fft.rfftfreq(n, d=1/sample_rate)
        IR_fft[freqs >= params["cutoff"]] *= params["shelf_gain"]
        return np.fft.irfft(IR_fft, n=n)

    def process_ir(self, ir, style="American", sample_rate=48048):
        ir = self.apply_resonators(ir, sample_rate)
        ir = self.apply_fixed_filters(ir, sample_rate)
        ir = self.apply_random_eq(ir, sample_rate)
        ir = self.apply_amp_simulation(ir, sample_rate)
        ir = self.suppress_unpleasant_resonances(ir, sample_rate)
        ir = self.apply_style(ir, style, sample_rate)
        ir = self.apply_high_shelf(ir, sample_rate)
        norm_factor = np.max(np.abs(ir)) + 1e-9
        return ir / norm_factor

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        # Get IRs from the dataset based on style
        irs = self.ir_reader.get_irs_by_style(style, count)

        # Apply post-processing to each IR
        processed_irs = []
        for ir in irs:
            # Apply random gain variation
            gain = np.random.uniform(0.9, 1.1)
            ir = ir * gain

            # Apply post-processing (same as before but without ML model)
            ir = self.process_ir(ir, style=style, sample_rate=48000)
            processed_irs.append(ir)

        return processed_irs

# --- IR Generation Worker ---
class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, exporter, style, count, seed=None):
        super().__init__()
        self.exporter = exporter
        self.style = style
        self.count = count
        self.seed = seed

    @pyqtSlot()
    def run(self):
        try:
            if self.seed is not None:
                np.random.seed(self.seed)
            logging.info("Starting IR selection with style=%s, count=%d", self.style, self.count)
            irs = self.exporter.generate_multiple_irs(count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            logging.error("Error during IR selection: %s", str(e))
            self.error.emit(str(e))

# --- Main GUI Class ---
class IRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist")
        self.setGeometry(100, 100, 1400, 950)
        self.last_generated_irs = None
        self.ir_widgets = []
        self.selected_style = "American"
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # --- Central widget with carbon background ---
        main_widget = CarbonWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # --- Header Section ---
        header_layout = QVBoxLayout()
        title_layout = QHBoxLayout()
        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet("font-size: 48px; font-weight: bold; color: #FFFFFF;")
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setOffset(5, 5)
        shadow.setColor(QColor(0, 0, 0, 220))
        title_label.setGraphicsEffect(shadow)
        company_label = QLabel("by Develop Device")
        company_label.setStyleSheet("font-size: 24px; font-style: italic; color: #AAAAAA; margin-left: 10px;")
        title_layout.addStretch()
        title_layout.addWidget(title_label)
        title_layout.addWidget(company_label)
        title_layout.addStretch()
        header_layout.addLayout(title_layout)
        
        # Add subheading
        subheading_label = QLabel("Advanced Cabinet IR Generator for Guitarists Powered by AI")
        subheading_label.setStyleSheet("font-size: 20px; color: #FFFFFF;")
        subheading_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subheading_label)
        
        header_layout.addSpacing(10)
        instructions = QLabel(
            "Select the IR sound characteristic below and click the 'Generate IRs' button to create 10 new cabinet IRs.\n"
            "Use the IR list on the left to select and preview individual IRs. The detail panel on the right displays the EQ curve and spectrogram of the selected IR."
        )
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("font-size: 16px; color: #EEEEEE;")
        instructions.setWordWrap(True)
        header_layout.addWidget(instructions)
        main_layout.addLayout(header_layout)

        # --- Combined IR Sound Characteristic selection and Generate Button ---
        # Create a horizontal layout with fixed height of 50 for both parts.
        style_group_box = QGroupBox("Select IR Sound Characteristic")
        style_group_box.setStyleSheet("""
            QGroupBox { 
                background-color: #444444; 
                font-size: 18px; 
                font-weight: bold; 
                color: #FFFFFF; 
                border: 2px solid #3F6AC2; 
                border-radius: 5px; 
                margin-top: 0.5em;
            } 
            QGroupBox::title { 
                subcontrol-origin: margin; 
                subcontrol-position: top center; 
                padding: 0 3px; 
                color: #FFFFFF; 
            }
        """)
        style_layout = QHBoxLayout()
        self.style_button_group = QButtonGroup()
        # Radio buttons in order: American, British, German, Random
        self.radio_american = QRadioButton("American")
        self.radio_british = QRadioButton("British")
        self.radio_german = QRadioButton("German")
        self.radio_custom = QRadioButton("Random")
        self.radio_american.setChecked(True)
        self.radio_american.setToolTip("Select the American cabinet IR style.")
        self.radio_british.setToolTip("Select the British cabinet IR style.")
        self.radio_german.setToolTip("Select the German cabinet IR style.")
        self.radio_custom.setToolTip("Select a random cabinet IR style.")
        for rb in (self.radio_american, self.radio_british, self.radio_german, self.radio_custom):
            rb.setStyleSheet("QRadioButton { font-size: 16px; color: #FFFFFF; } QRadioButton::indicator { width: 18px; height: 18px; } QRadioButton::indicator:unchecked { border: 1px solid #555555; background-color: #333333; border-radius: 9px; } QRadioButton::indicator:checked { border: 1px solid #007ACC; background-color: #007ACC; border-radius: 9px; }")
            self.style_button_group.addButton(rb)
        self.radio_american.toggled.connect(lambda: self.update_style("American"))
        self.radio_british.toggled.connect(lambda: self.update_style("British"))
        self.radio_german.toggled.connect(lambda: self.update_style("German"))
        self.radio_custom.toggled.connect(lambda: self.update_style("Random"))
        style_layout.addWidget(self.radio_american)
        style_layout.addWidget(self.radio_british)
        style_layout.addWidget(self.radio_german)
        style_layout.addWidget(self.radio_custom)
        style_group_box.setLayout(style_layout)
        style_group_box.setFixedHeight(50)

        self.generate_round_btn = QPushButton("Click here to generate 10 new cabinet IRs")
        self.generate_round_btn.setFixedHeight(50)
        self.generate_round_btn.setToolTip("Generate 10 new cabinet impulse responses using AI.")
        self.generate_round_btn.setStyleSheet("""
            QPushButton {
                background-color: #5481EA;
                border: 2px solid #3F6AC2;
                border-radius: 10px;
                font-size: 18px;
                font-weight: bold;
                color: #FFFFFF;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #6B9DFA;
            }
            QPushButton:pressed {
                background-color: #4B78D0;
                border-style: inset;
            }
            QPushButton:disabled {
                background-color: #666666;
                border: 2px solid #555555;
                color: #AAAAAA;
            }
        """)
        self.generate_round_btn.clicked.connect(self.update_irs)
        
        # Horizontal layout with 4:1 ratio (4/5 for group box, 1/5 for button)
        style_and_generate_layout = QHBoxLayout()
        style_and_generate_layout.addWidget(style_group_box, 4)
        style_and_generate_layout.addWidget(self.generate_round_btn, 1)
        style_and_generate_layout.setAlignment(Qt.AlignVCenter)
        main_layout.addLayout(style_and_generate_layout)

        # --- Main Splitter ---
        self.ir_selection_group = QGroupBox("Select IRs")
        self.ir_selection_group.setStyleSheet("QGroupBox { font-size: 18px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        ir_selection_layout = QVBoxLayout()
        self.checkbox_grid = QGridLayout()
        ir_selection_layout.addLayout(self.checkbox_grid)
        self.ir_selection_group.setLayout(ir_selection_layout)
        detail_group = QGroupBox("IR Detail Preview")
        detail_group.setStyleSheet("QGroupBox { font-size: 18px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        detail_layout = QVBoxLayout()
        self.detail_canvas = DetailCanvas(self, dpi=100)
        detail_layout.addWidget(self.detail_canvas)
        detail_group.setLayout(detail_layout)
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.ir_selection_group)
        splitter.addWidget(detail_group)
        # Set fixed ratio: left section 1/4, right section 3/4
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 3)
        main_layout.addWidget(splitter)

        # --- Export and Preview Controls (Preview left, Export right) ---
        controls_layout = QHBoxLayout()

        # Preview Controls (left)
        preview_group = QGroupBox("Preview Controls")
        preview_group.setStyleSheet("QGroupBox { font-size: 16px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        preview_layout = QHBoxLayout()
        self.previewCombinedBtn = QPushButton("Preview Combined")
        self.previewCombinedBtn.setToolTip("Play a combined preview of the selected IRs.")
        self.stopPreviewBtn = QPushButton("Stop Preview")
        self.stopPreviewBtn.setToolTip("Stop the audio preview.")
        for btn in (self.previewCombinedBtn, self.stopPreviewBtn):
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #D3D3D3;
                    color: #000000;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A9A9A9;
                }
            """)
        self.previewCombinedBtn.clicked.connect(self.preview_combined_irs)
        self.stopPreviewBtn.clicked.connect(self.stop_preview)
        preview_layout.addWidget(self.previewCombinedBtn)
        preview_layout.addWidget(self.stopPreviewBtn)
        preview_group.setLayout(preview_layout)
        controls_layout.addWidget(preview_group)

        # Export Controls (right)
        export_group = QGroupBox("Export Controls")
        export_group.setStyleSheet("QGroupBox { font-size: 16px; font-weight: bold; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; margin-top: 0.5em; } QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; color: #FFFFFF; }")
        export_layout = QHBoxLayout()
        self.saveSelectedBtn = QPushButton("Export Selected IRs to WAV")
        self.saveSelectedBtn.setToolTip("Export the selected IRs as individual WAV files.")
        self.saveAllBtn = QPushButton("Export All IRs to WAV")
        self.saveAllBtn.setToolTip("Export all generated IRs as individual WAV files.")
        self.exportCombinedBtn = QPushButton("Export Combined IR to WAV")
        self.exportCombinedBtn.setToolTip("Export a single WAV file that is the combined average of the selected IRs.")
        for btn in (self.saveSelectedBtn, self.saveAllBtn, self.exportCombinedBtn):
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #D3D3D3;
                    color: #000000;
                    border: 1px solid #555555;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A9A9A9;
                }
            """)
        self.saveSelectedBtn.clicked.connect(self.save_selected_irs)
        self.saveAllBtn.clicked.connect(self.save_all_irs)
        self.exportCombinedBtn.clicked.connect(self.export_combined_irs)
        export_layout.addWidget(self.saveSelectedBtn)
        export_layout.addWidget(self.saveAllBtn)
        export_layout.addWidget(self.exportCombinedBtn)
        export_group.setLayout(export_layout)
        controls_layout.addWidget(export_group)

        main_layout.addLayout(controls_layout)
        
        # --- Bottom section with copyright ---
        copyright_label = QLabel()
        copyright_label.setTextFormat(Qt.RichText)
        copyright_label.setText(
            '© 2025 <a href="https://developdevice.com" style="color: #FFFFFF; text-decoration: none;">Develop Device</a>. All rights reserved.'
        )
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("font-size: 12px; color: #FFFFFF;")
        copyright_label.setOpenExternalLinks(True)
        main_layout.addWidget(copyright_label)
        
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        self.exporter = IRExporter()
        self.update_irs()

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def update_style(self, style):
        if self.sender().isChecked():
            self.selected_style = style

    def update_irs(self):
        # Use default seed = None for randomization
        seed = None

        self.generate_round_btn.setEnabled(False)
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(exporter=self.exporter, style=self.selected_style, count=10, seed=seed)
        self.worker.moveToThread(self.worker_thread)
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        self.worker_thread.start()

    @pyqtSlot(list)
    def on_generation_finished(self, irs):
        self.last_generated_irs = irs
        self.update_ir_list()
        self.generate_round_btn.setEnabled(True)
        logging.info("IR generation completed.")

    @pyqtSlot(str)
    def on_generation_error(self, error_message):
        self.show_message("Error", f"Error during IR generation: {error_message}")
        self.generate_round_btn.setEnabled(True)

    def update_ir_list(self):
        for i in reversed(range(self.checkbox_grid.count())):
            widget = self.checkbox_grid.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        self.ir_widgets = []
        total = len(self.last_generated_irs)
        rows = 5
        cols = (total + rows - 1) // rows
        for i in range(total):
            container = QWidget()
            h_layout = QHBoxLayout(container)
            checkbox = DarkCheckBox(f"IR {i+1}")
            checkbox.setToolTip(f"Select IR {i+1}")
            preview_btn = QPushButton("Preview")
            preview_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #000000;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: #F0F0F0;
                }
                QPushButton:pressed {
                    background-color: #E0E0E0;
                }
            """)
            preview_btn.setToolTip("Play audio preview of this impulse response")
            preview_btn.clicked.connect(lambda checked, idx=i: self.preview_single_ir(idx))
            h_layout.addWidget(checkbox)
            h_layout.addStretch()
            h_layout.addWidget(preview_btn)
            row = i % rows
            col = i // rows
            self.checkbox_grid.addWidget(container, row, col)
            self.ir_widgets.append({'checkbox': checkbox, 'preview_button': preview_btn})

    def save_selected_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "No IR selected for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in selected_indices:
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                sf.write(filename, self.last_generated_irs[i], 48000, subtype='PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "Selected IRs were successfully exported as WAV files.")

    def save_all_irs(self):
        if not self.last_generated_irs:
            self.show_message("Warning", "No IR available for exporting.")
            return
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export IRs")
        if not folder:
            return
        for i in range(len(self.last_generated_irs)):
            base_filename = f"IR_{i+1}.wav"
            filename = self.get_unique_filename(folder, base_filename)
            try:
                sf.write(filename, self.last_generated_irs[i], 48000, subtype='PCM_24')
            except Exception as e:
                self.show_message("Error", f"Error exporting {filename}: {str(e)}")
                return
        self.show_message("Success", "All IRs were successfully exported as WAV files.")

    def export_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if len(selected_indices) < 1:
            self.show_message("Warning", "Select at least one IR to export combined IR.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        folder = QFileDialog.getExistingDirectory(self, "Select folder to export combined IR")
        if not folder:
            return
        base_filename = "Combined_IR.wav"
        filename = self.get_unique_filename(folder, base_filename)
        try:
            sf.write(filename, combined_ir, 48000, subtype='PCM_24')
        except Exception as e:
            self.show_message("Error", f"Error exporting combined IR: {str(e)}")
            return
        self.show_message("Success", "Combined IR was successfully exported as a WAV file.")

    def preview_single_ir(self, idx):
        if idx < 0 or idx >= len(self.last_generated_irs):
            self.show_message("Warning", "The selected IR is not valid.")
            return
        ir = self.last_generated_irs[idx]
        self._preview_ir(ir)
        self.detail_canvas.update_detail(ir)

    def preview_combined_irs(self):
        selected_indices = [i for i, widget in enumerate(self.ir_widgets) if widget['checkbox'].isChecked()]
        if not selected_indices:
            self.show_message("Warning", "Select at least one IR for combined preview.")
            return
        combined_ir = np.mean([self.last_generated_irs[i] for i in selected_indices], axis=0)
        combined_ir /= (np.max(np.abs(combined_ir)) + 1e-9)
        self._preview_ir(combined_ir)
        self.detail_canvas.update_detail(combined_ir)

    def _preview_ir(self, ir):
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            sample_data, sample_rate = sf.read(sample_path)
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return
        convolved = fftconvolve(sample_data, ir, mode="full")
        convolved = convolved / (np.max(np.abs(convolved)) + 1e-9) * 0.9
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()
        try:
            sf.write(temp_filename, convolved, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return
        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def _on_media_status_changed(self, status):
        if status in (QMediaPlayer.EndOfMedia, QMediaPlayer.InvalidMedia, QMediaPlayer.NoMedia):
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def stop_preview(self):
        self.player.stop()

    def show_message(self, title, message):
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet("QMessageBox {background-color: #121212; color: #FFFFFF;} QMessageBox QLabel {color: #FFFFFF;} QPushButton { background-color: #333333; color: #FFFFFF; border: 1px solid #555555; border-radius: 5px; font-weight: bold; font-size: 14px; padding: 8px 12px;} QPushButton:hover {background-color: #444444;} QPushButton:pressed { background-color: #555555;}")
        msg_box.exec_()

    def closeEvent(self, event):
        if self.worker_thread is not None:
            try:
                if self.worker_thread.isRunning():
                    self.worker_thread.quit()
                    self.worker_thread.wait()
            except RuntimeError:
                pass
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()


if __name__ == "__main__":
    try:
        app = QApplication(sys.argv)
        font_path = os.path.join(os.path.dirname(__file__), "Exo-VariableFont_wght.ttf")
        font_id = QFontDatabase.addApplicationFont(font_path)
        if font_id != -1:
            families = QFontDatabase.applicationFontFamilies(font_id)
            if families:
                default_font = QFont(families[0])
                app.setFont(default_font)
                print("Loaded font:", families[0])
        else:
            print("Failed to load Exo-VariableFont_wght.ttf")

        print("Creating IRGeneratorGUI...")
        window = IRGeneratorGUI()
        print("Showing window...")
        window.show()
        print("Starting application...")
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Application error: {e}")
        import traceback
        traceback.print_exc()
